# Unearthed Obsidian Plugin (Kindle Sync)

Unearthed.app is a free service that seamlessly syncs your Kindle highlights and notes to your preferred platforms.
A **Daily Reflection** is served to you that consists of a quote, your note, book, author and location. This plugin allows you to use custom templates determine how book will appear in Obsidian.

Currently, Unearthed supports syncing with:

- **Obsidian** (via this plugin)
- **Notion**
- **Capacities**
- **Supernotes**
- And, of course, the **Unearthed** app itself.

**Important**: Only the books visible in your Kindle notebook at [Amazon Kindle Notebook](https://read.amazon.com/notebook) will be synced.

Looking to expand support to more sources and offer additional integrations in the future.

## Tags

**Premium** Unearthed users can now also sync their Tags. This will automatically create tag files in Obsidian and link them to your books.

## Video Tutorial (follow along)

[![Video Tutorial](https://img.youtube.com/vi/uilUlt4wRVs/maxresdefault.jpg)](https://www.youtube.com/watch?v=uilUlt4wRVs)

## Daily Reflection

![image](https://github.com/user-attachments/assets/e0bb8af3-1d8c-4037-a38a-89a339b371f4)

## All Quotes and Notes Synced

![image](https://github.com/user-attachments/assets/50bd5fc9-c13e-4c8c-86db-ddba0a88a4cd)

## How to Sync Your Kindle Books with Obsidian

1. Create an account on [unearthed.app](https://unearthed.app).
2. Follow the prompts to sync your Kindle data to Unearthed (this step requires installing a browser extension and creating an Unearthed API key).
3. Install the Unearthed plugin in your Obsidian vault.
4. Open the plugin settings in Obsidian and paste in your API key, then press "Sync."
5. You can set it to auto-sync each time Obsidian loads, or choose to sync manually from the side panel or the plugin settings.

## Open Source & Contributions

Unearthed.app, the browser extension, and the Obsidian plugin are all **open source**. I welcome contributions, feedback, and suggestions from the community. If you'd like to get involved, check out our repositories and feel free to submit a pull request or open an issue.
